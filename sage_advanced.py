import speech_recognition as sr
import os
import webbrowser
import requests
import json
import pyttsx3
from config import apikey, EMAIL_CONFIG, CONTACTS
import datetime
import random
import smtplib
import subprocess
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import urllib.parse
import threading
import queue
import logging
from typing import Dict, List, Tuple, Optional
import asyncio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SageAI:
    """Advanced Sage AI Assistant"""
    
    def __init__(self):
        self.chat_history = []
        self.tts_engine = None
        self.is_listening = False
        self.command_queue = queue.Queue()
        self.response_queue = queue.Queue()
        self.initialize_tts()
        
    def initialize_tts(self):
        """Initialize text-to-speech engine"""
        try:
            self.tts_engine = pyttsx3.init()
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Try to set a female voice if available
                for voice in voices:
                    if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            self.tts_engine.setProperty('rate', 180)  # Speed
            self.tts_engine.setProperty('volume', 0.9)  # Volume
        except Exception as e:
            logger.error(f"TTS initialization error: {e}")
    
    def say(self, text: str) -> None:
        """Text-to-speech with threading"""
        try:
            if self.tts_engine:
                def speak():
                    self.tts_engine.say(text)
                    self.tts_engine.runAndWait()
                
                thread = threading.Thread(target=speak)
                thread.daemon = True
                thread.start()
        except Exception as e:
            logger.error(f"TTS error: {e}")
    
    def listen_for_command(self, timeout: int = 5) -> str:
        """Listen for voice commands with improved error handling"""
        r = sr.Recognizer()
        try:
            with sr.Microphone() as source:
                r.adjust_for_ambient_noise(source, duration=1)
                r.pause_threshold = 1
                r.energy_threshold = 300
                
                audio = r.listen(source, timeout=timeout, phrase_time_limit=10)
                query = r.recognize_google(audio, language="en-in")
                return query.lower()
                
        except sr.WaitTimeoutError:
            return "timeout"
        except sr.UnknownValueError:
            return "unknown"
        except sr.RequestError as e:
            logger.error(f"Speech recognition error: {e}")
            return "error"
        except Exception as e:
            logger.error(f"Listening error: {e}")
            return "error"
    
    def chat_with_ai(self, query: str) -> str:
        """Enhanced chat with AI using conversation history"""
        try:
            # Build conversation context
            messages = [
                {
                    "role": "system",
                    "content": "You are Sage, an advanced AI assistant for Sohom. You are helpful, intelligent, and conversational. Keep responses concise but informative. Remember previous conversations."
                }
            ]
            
            # Add recent chat history for context
            for chat in self.chat_history[-5:]:  # Last 5 exchanges
                messages.append({"role": "user", "content": chat["user"]})
                messages.append({"role": "assistant", "content": chat["assistant"]})
            
            # Add current query
            messages.append({"role": "user", "content": query})
            
            # API request
            url = "https://openrouter.ai/api/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {apikey}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "mistralai/devstral-small:free",
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 300,
                "top_p": 1,
                "frequency_penalty": 0.1,
                "presence_penalty": 0.1
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                ai_response = result['choices'][0]['message']['content']
                
                # Store in chat history
                self.chat_history.append({
                    "user": query,
                    "assistant": ai_response,
                    "timestamp": datetime.datetime.now().isoformat()
                })
                
                return ai_response
            else:
                return "I'm having trouble connecting to my AI brain right now."
                
        except Exception as e:
            logger.error(f"Chat AI error: {e}")
            return "Sorry, I encountered an error while processing your request."
    
    def send_email(self, to_email: str, subject: str, body: str) -> bool:
        """Enhanced email sending with better error handling"""
        try:
            from_email = EMAIL_CONFIG["from_email"]
            password = EMAIL_CONFIG["password"]
            
            msg = MIMEMultipart()
            msg['From'] = from_email
            msg['To'] = to_email
            msg['Subject'] = subject
            msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])
            server.starttls()
            server.login(from_email, password)
            server.sendmail(from_email, to_email, msg.as_string())
            server.quit()
            
            logger.info(f"Email sent to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Email error: {e}")
            return False
    
    def craft_email_with_ai(self, recipient: str, purpose: str, tone: str = "professional") -> Tuple[str, str]:
        """AI-powered email crafting with tone control"""
        try:
            prompt = f"""Write a {tone} email for the following:

Recipient: {recipient}
Purpose: {purpose}
Sender: Sohom

Please write a complete email with appropriate subject line and body.
Format as:
Subject: [subject]
Body: [body]
"""
            
            url = "https://openrouter.ai/api/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {apikey}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": "mistralai/devstral-small:free",
                "messages": [
                    {
                        "role": "system",
                        "content": f"You are a professional email writing assistant. Write clear, {tone} emails."
                    },
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.7,
                "max_tokens": 400
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # Parse subject and body
                lines = content.split('\n')
                subject = "Message from Sohom"
                body = content
                
                for i, line in enumerate(lines):
                    if line.startswith("Subject:"):
                        subject = line.replace("Subject:", "").strip()
                        body = '\n'.join(lines[i+1:]).strip()
                        if body.startswith("Body:"):
                            body = body.replace("Body:", "").strip()
                        break
                
                return subject, body
            else:
                return "Message from Sohom", f"Hi,\n\n{purpose}\n\nBest regards,\nSohom"
                
        except Exception as e:
            logger.error(f"Email crafting error: {e}")
            return "Message from Sohom", f"Hi,\n\n{purpose}\n\nBest regards,\nSohom"
    
    def play_music(self, song_name: str = "", service: str = "spotify") -> bool:
        """Enhanced music playing with multiple services"""
        try:
            if song_name:
                encoded_query = urllib.parse.quote(song_name)
                
                if "spotify" in service.lower():
                    try:
                        os.system(f'start spotify:search:{encoded_query}')
                        self.say(f"Playing {song_name} on Spotify")
                    except:
                        webbrowser.open(f"https://open.spotify.com/search/{encoded_query}")
                        self.say(f"Opening {song_name} on Spotify web")
                        
                elif "youtube" in service.lower():
                    webbrowser.open(f"https://music.youtube.com/search?q={encoded_query}")
                    self.say(f"Playing {song_name} on YouTube Music")
                    
                elif "apple" in service.lower():
                    webbrowser.open(f"https://music.apple.com/search?term={encoded_query}")
                    self.say(f"Playing {song_name} on Apple Music")
                    
            else:
                if "spotify" in service.lower():
                    try:
                        os.system('start spotify:')
                    except:
                        webbrowser.open("https://open.spotify.com")
                elif "youtube" in service.lower():
                    webbrowser.open("https://music.youtube.com")
                elif "apple" in service.lower():
                    webbrowser.open("https://music.apple.com")
                    
                self.say(f"Opening {service}")
            
            return True
            
        except Exception as e:
            logger.error(f"Music error: {e}")
            self.say("Sorry, I couldn't play music right now")
            return False
    
    def open_application(self, app_name: str) -> bool:
        """Enhanced application opening"""
        apps = {
            "notepad": "notepad",
            "calculator": "calc",
            "paint": "mspaint",
            "chrome": "chrome",
            "firefox": "firefox",
            "edge": "msedge",
            "explorer": "explorer",
            "cmd": "cmd",
            "powershell": "powershell",
            "word": "winword",
            "excel": "excel",
            "powerpoint": "powerpnt",
            "outlook": "outlook",
            "teams": "ms-teams:",
            "settings": "ms-settings:",
            "store": "ms-windows-store:",
        }
        
        app_name = app_name.lower()
        if app_name in apps:
            try:
                if app_name in ["teams", "settings", "store"]:
                    os.system(f"start {apps[app_name]}")
                else:
                    subprocess.Popen(apps[app_name])
                self.say(f"Opening {app_name}")
                return True
            except Exception as e:
                logger.error(f"App opening error: {e}")
                self.say(f"Sorry, I couldn't open {app_name}")
                return False
        else:
            self.say(f"I don't know how to open {app_name}")
            return False
    
    def get_weather(self, city: str = "New York") -> str:
        """Get weather information (placeholder - would need weather API)"""
        # This is a placeholder - you'd integrate with a weather API like OpenWeatherMap
        return f"I'd need a weather API key to get real weather data for {city}. This is a placeholder response."
    
    def set_reminder(self, reminder_text: str, time_str: str) -> bool:
        """Set a reminder (placeholder - would need scheduling system)"""
        # This is a placeholder - you'd integrate with a task scheduler
        self.say(f"I'd set a reminder for '{reminder_text}' at {time_str}, but I need a scheduling system for this feature.")
        return True
    
    def get_news(self, category: str = "general") -> str:
        """Get news headlines (placeholder - would need news API)"""
        # This is a placeholder - you'd integrate with a news API
        return f"I'd fetch {category} news for you, but I need a news API key for this feature."

# Global instance
sage = SageAI()
