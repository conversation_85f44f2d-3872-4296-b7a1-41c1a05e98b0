import speech_recognition as sr
import os
import webbrowser
import requests
import json
import pyttsx3
from config import apikey, EMAIL_CONFIG, CONTACTS
import datetime
import random
import numpy as np
import smtplib
import subprocess
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.base import MIMEBase
from email import encoders
import urllib.parse
import time
import threading
import queue
import logging
from typing import Dict, List, Tuple, Optional
import asyncio
import re
import psutil
import pyautogui
import schedule
import pickle
import sqlite3
from pathlib import Path
import cv2
import winreg
import winsound
import socket
import platform
import speedtest
import geocoder
import wikipedia
import yfinance as yf


# Global variables
chatStr = ""
tts_engine = None
is_speaking = False
should_stop_speaking = False
conversation_active = False
last_interaction_time = None
continuous_listening = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sage.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize TTS engine
try:
    tts_engine = pyttsx3.init()
    voices = tts_engine.getProperty('voices')
    if voices:
        for voice in voices:
            if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                tts_engine.setProperty('voice', voice.id)
                break
    tts_engine.setProperty('rate', 180)
    tts_engine.setProperty('volume', 0.9)
except Exception as e:
    print(f"TTS setup error: {e}")

class AdvancedSageAI:
    """Advanced Sage AI Assistant with enhanced capabilities"""

    def __init__(self):
        self.chat_history = []
        self.user_preferences = {}
        self.reminders = []
        self.is_listening = False
        self.conversation_context = []
        self.command_history = []
        self.initialize_components()
        self.load_user_data()

    def initialize_components(self):
        """Initialize all AI components"""
        try:
            # Initialize TTS
            self.tts_engine = pyttsx3.init()
            self.setup_voice()

            # Initialize database
            self.init_database()

            # Initialize scheduler
            self.scheduler = schedule

            logger.info("Sage AI components initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing components: {e}")

    def setup_voice(self):
        """Setup advanced voice configuration"""
        try:
            voices = self.tts_engine.getProperty('voices')

            # Try to find a female voice
            for voice in voices:
                if any(keyword in voice.name.lower() for keyword in ['zira', 'female', 'woman']):
                    self.tts_engine.setProperty('voice', voice.id)
                    break

            # Voice settings
            self.tts_engine.setProperty('rate', 180)  # Speed
            self.tts_engine.setProperty('volume', 0.9)  # Volume

        except Exception as e:
            logger.error(f"Voice setup error: {e}")

    def init_database(self):
        """Initialize SQLite database for storing user data"""
        try:
            self.db_path = Path("sage_data.db")
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            cursor = self.conn.cursor()

            # Create tables
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    user_input TEXT,
                    sage_response TEXT,
                    context TEXT
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reminders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    reminder_text TEXT,
                    reminder_time TEXT,
                    is_completed BOOLEAN DEFAULT FALSE,
                    created_at TEXT
                )
            ''')

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_preferences (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
            ''')

            self.conn.commit()
            logger.info("Database initialized successfully")

        except Exception as e:
            logger.error(f"Database initialization error: {e}")

    def save_conversation(self, user_input: str, sage_response: str, context: str = ""):
        """Save conversation to database"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT INTO conversations (timestamp, user_input, sage_response, context)
                VALUES (?, ?, ?, ?)
            ''', (datetime.datetime.now().isoformat(), user_input, sage_response, context))
            self.conn.commit()
        except Exception as e:
            logger.error(f"Error saving conversation: {e}")

    def load_user_data(self):
        """Load user preferences and data"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('SELECT key, value FROM user_preferences')
            preferences = cursor.fetchall()

            for key, value in preferences:
                try:
                    self.user_preferences[key] = json.loads(value)
                except:
                    self.user_preferences[key] = value

            logger.info("User data loaded successfully")

        except Exception as e:
            logger.error(f"Error loading user data: {e}")

    def save_user_preference(self, key: str, value):
        """Save user preference to database"""
        try:
            cursor = self.conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO user_preferences (key, value)
                VALUES (?, ?)
            ''', (key, json.dumps(value) if isinstance(value, (dict, list)) else str(value)))
            self.conn.commit()
            self.user_preferences[key] = value
        except Exception as e:
            logger.error(f"Error saving preference: {e}")

    def say(self, text: str, interrupt: bool = False):
        """Advanced text-to-speech with interruption capability"""
        try:
            if interrupt:
                self.tts_engine.stop()

            # Add personality to responses
            if "sohom" not in text.lower() and random.random() < 0.3:
                text = f"Sohom, {text}"

            def speak():
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()

            thread = threading.Thread(target=speak)
            thread.daemon = True
            thread.start()

            logger.info(f"Sage speaking: {text}")

        except Exception as e:
            logger.error(f"TTS error: {e}")

    def listen_for_command(self, timeout: int = 8) -> str:
        """Advanced voice recognition with noise filtering"""
        r = sr.Recognizer()

        try:
            with sr.Microphone() as source:
                print("🎤 Adjusting for ambient noise...")
                r.adjust_for_ambient_noise(source, duration=1.5)
                r.pause_threshold = 1.2
                r.energy_threshold = 400
                r.dynamic_energy_threshold = True

                print("🎧 Listening...")
                audio = r.listen(source, timeout=timeout, phrase_time_limit=15)

                print("🧠 Processing speech...")

                # Try multiple recognition engines
                try:
                    query = r.recognize_google(audio, language="en-in")
                    logger.info(f"Voice command recognized: {query}")
                    return query.lower().strip()
                except:
                    # Fallback to different language model
                    query = r.recognize_google(audio, language="en-us")
                    logger.info(f"Voice command recognized (fallback): {query}")
                    return query.lower().strip()

        except sr.WaitTimeoutError:
            logger.warning("Voice recognition timeout")
            return "timeout"
        except sr.UnknownValueError:
            logger.warning("Could not understand audio")
            return "unknown"
        except sr.RequestError as e:
            logger.error(f"Speech recognition service error: {e}")
            return "error"
        except Exception as e:
            logger.error(f"Listening error: {e}")
            return "error"

# https://youtu.be/Z3ZAJoi4x6Q
def chat(query):
    global chatStr
    print(chatStr)
    chatStr += f"Sohom: {query}\n Sage: "

    try:
        # OpenRouter API endpoint
        url = "https://openrouter.ai/api/v1/chat/completions"

        # Headers for OpenRouter API
        headers = {
            "Authorization": f"Bearer {apikey}",
            "Content-Type": "application/json"
        }

        # Request payload for Mistral model
        data = {
            "model": "mistralai/devstral-small:free",
            "messages": [
                {
                    "role": "system",
                    "content": "You are Sage, a helpful AI assistant for Sohom. Keep responses concise and conversational. Address Sohom by name when appropriate."
                },
                {
                    "role": "user",
                    "content": chatStr + query
                }
            ],
            "temperature": 0.7,
            "max_tokens": 256,
            "top_p": 1,
            "frequency_penalty": 0,
            "presence_penalty": 0
        }

        # Make the API request
        response = requests.post(url, headers=headers, json=data)

        if response.status_code == 200:
            result = response.json()
            response_text = result['choices'][0]['message']['content']
            say(response_text)
            chatStr += f"{response_text}\n"
            return response_text
        else:
            error_msg = f"API Error: {response.status_code}"
            say(error_msg)
            return error_msg

    except Exception as e:
        error_msg = f"Error occurred: {str(e)}"
        say(error_msg)
        return error_msg


def ai(prompt):
    try:
        text = f"Mistral AI response for Prompt: {prompt} \n *************************\n\n"

        # OpenRouter API endpoint
        url = "https://openrouter.ai/api/v1/chat/completions"

        # Headers for OpenRouter API
        headers = {
            "Authorization": f"Bearer {apikey}",
            "Content-Type": "application/json"
        }

        # Request payload for Mistral model
        data = {
            "model": "mistralai/devstral-small:free",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 256,
            "top_p": 1,
            "frequency_penalty": 0,
            "presence_penalty": 0
        }

        # Make the API request
        response = requests.post(url, headers=headers, json=data)

        if response.status_code == 200:
            result = response.json()
            response_text = result['choices'][0]['message']['content']
            text += response_text

            if not os.path.exists("Openai"):
                os.mkdir("Openai")

            # Create filename from prompt
            filename = ''.join(prompt.split('intelligence')[1:]).strip() if 'intelligence' in prompt else f"prompt-{random.randint(1, 2343434356)}"
            filename = filename[:50] if filename else f"prompt-{random.randint(1, 2343434356)}"  # Limit filename length

            with open(f"Openai/{filename}.txt", "w", encoding="utf-8") as f:
                f.write(text)

            print(f"Response saved to Openai/{filename}.txt")
        else:
            print(f"API Error: {response.status_code}")

    except Exception as e:
        print(f"Error in AI function: {str(e)}")

def send_email(to_email, subject, body):
    """Send email using Gmail SMTP"""
    try:
        # Use email config from config.py
        from_email = EMAIL_CONFIG["from_email"]
        password = EMAIL_CONFIG["password"]

        # Create message
        msg = MIMEMultipart()
        msg['From'] = from_email
        msg['To'] = to_email
        msg['Subject'] = subject

        # Add body to email
        msg.attach(MIMEText(body, 'plain'))

        # Gmail SMTP configuration
        server = smtplib.SMTP(EMAIL_CONFIG["smtp_server"], EMAIL_CONFIG["smtp_port"])
        server.starttls()  # Enable security
        server.login(from_email, password)

        # Send email
        text = msg.as_string()
        server.sendmail(from_email, to_email, text)
        server.quit()

        say(f"Email sent successfully to {to_email}")
        print(f"Email sent to {to_email}")
        return True

    except Exception as e:
        say("Sorry, I couldn't send the email. Please check your email configuration.")
        print(f"Email error: {str(e)}")
        return False

def get_contact_email(contact_name):
    """Get email address from contacts"""
    contact_name = contact_name.lower()
    if contact_name in CONTACTS:
        return CONTACTS[contact_name]
    return None

def play_music_on_spotify(song_name="", artist_name=""):
    """Play music on Spotify"""
    try:
        if song_name:
            # Create search query
            if artist_name:
                search_query = f"{song_name} {artist_name}"
            else:
                search_query = song_name

            # URL encode the search query
            encoded_query = urllib.parse.quote(search_query)

            # Spotify search URL
            spotify_url = f"https://open.spotify.com/search/{encoded_query}"

            # Try to open Spotify app first, then web
            try:
                # Try to open in Spotify app
                os.system(f'start spotify:search:{encoded_query}')
                say(f"Playing {song_name} on Spotify")
            except:
                # Fallback to web version
                webbrowser.open(spotify_url)
                say(f"Opening {song_name} on Spotify web")
        else:
            # Open Spotify without specific song
            try:
                os.system('start spotify:')
                say("Opening Spotify")
            except:
                webbrowser.open("https://open.spotify.com")
                say("Opening Spotify web")
        return True
    except Exception as e:
        say("Sorry, I couldn't open Spotify")
        print(f"Spotify error: {str(e)}")
        return False

def play_music_on_youtube(song_name="", artist_name=""):
    """Play music on YouTube Music"""
    try:
        if song_name:
            # Create search query
            if artist_name:
                search_query = f"{song_name} {artist_name}"
            else:
                search_query = song_name

            # URL encode the search query
            encoded_query = urllib.parse.quote(search_query)

            # YouTube Music search URL
            yt_music_url = f"https://music.youtube.com/search?q={encoded_query}"

            webbrowser.open(yt_music_url)
            say(f"Playing {song_name} on YouTube Music")
        else:
            # Open YouTube Music without specific song
            webbrowser.open("https://music.youtube.com")
            say("Opening YouTube Music")
        return True
    except Exception as e:
        say("Sorry, I couldn't open YouTube Music")
        print(f"YouTube Music error: {str(e)}")
        return False

def play_music_on_apple_music(song_name="", artist_name=""):
    """Play music on Apple Music"""
    try:
        if song_name:
            # Create search query
            if artist_name:
                search_query = f"{song_name} {artist_name}"
            else:
                search_query = song_name

            # URL encode the search query
            encoded_query = urllib.parse.quote(search_query)

            # Apple Music search URL
            apple_music_url = f"https://music.apple.com/search?term={encoded_query}"

            webbrowser.open(apple_music_url)
            say(f"Playing {song_name} on Apple Music")
        else:
            # Open Apple Music without specific song
            webbrowser.open("https://music.apple.com")
            say("Opening Apple Music")
        return True
    except Exception as e:
        say("Sorry, I couldn't open Apple Music")
        print(f"Apple Music error: {str(e)}")
        return False

def play_music_generic(song_name="", artist_name="", service="spotify"):
    """Play music on specified service"""
    service = service.lower()

    if "spotify" in service:
        return play_music_on_spotify(song_name, artist_name)
    elif "youtube" in service or "yt" in service:
        return play_music_on_youtube(song_name, artist_name)
    elif "apple" in service:
        return play_music_on_apple_music(song_name, artist_name)
    else:
        # Default to Spotify
        return play_music_on_spotify(song_name, artist_name)

def craft_email_with_ai(recipient, purpose, additional_details=""):
    """Use AI to craft an email"""
    try:
        # Create a prompt for the AI to write an email
        prompt = f"""Write a professional email for the following:

Recipient: {recipient}
Purpose: {purpose}
Additional details: {additional_details}

Please write a complete email with:
- Appropriate subject line
- Professional greeting
- Clear and concise body
- Professional closing
- Sender signature as "Sohom"

Format the response as:
Subject: [subject line]
Body: [email body]
"""

        # OpenRouter API endpoint
        url = "https://openrouter.ai/api/v1/chat/completions"

        # Headers for OpenRouter API
        headers = {
            "Authorization": f"Bearer {apikey}",
            "Content-Type": "application/json"
        }

        # Request payload for Mistral model
        data = {
            "model": "mistralai/devstral-small:free",
            "messages": [
                {
                    "role": "system",
                    "content": "You are a professional email writing assistant. Write clear, concise, and professional emails."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 512,
            "top_p": 1,
            "frequency_penalty": 0,
            "presence_penalty": 0
        }

        # Make the API request
        response = requests.post(url, headers=headers, json=data)

        if response.status_code == 200:
            result = response.json()
            email_content = result['choices'][0]['message']['content']

            # Parse the email content to extract subject and body
            lines = email_content.split('\n')
            subject = ""
            body = ""

            for i, line in enumerate(lines):
                if line.startswith("Subject:"):
                    subject = line.replace("Subject:", "").strip()
                elif line.startswith("Body:"):
                    body = '\n'.join(lines[i+1:]).strip()
                    break

            # If parsing failed, use the entire content as body
            if not subject or not body:
                subject = f"Message from Sohom"
                body = email_content

            return subject, body
        else:
            return "Message from Sohom", f"Hi,\n\n{purpose}\n\n{additional_details}\n\nBest regards,\nSohom"

    except Exception as e:
        print(f"Email crafting error: {str(e)}")
        return "Message from Sohom", f"Hi,\n\n{purpose}\n\n{additional_details}\n\nBest regards,\nSohom"

def send_crafted_email(recipient_name, purpose, additional_details=""):
    """Craft and send an email using AI"""
    try:
        # Get recipient email
        to_email = get_contact_email(recipient_name)
        if not to_email:
            say(f"I don't have {recipient_name}'s email address in my contacts.")
            return False

        say("Let me craft an email for you...")

        # Craft the email using AI
        subject, body = craft_email_with_ai(recipient_name, purpose, additional_details)

        # Send the email
        success = send_email(to_email, subject, body)

        if success:
            say(f"Email successfully crafted and sent to {recipient_name}")
            print(f"Subject: {subject}")
            print(f"Body: {body}")

        return success

    except Exception as e:
        say("Sorry, I encountered an error while crafting and sending the email.")
        print(f"Crafted email error: {str(e)}")
        return False

# ==================== ADVANCED JARVIS FUNCTIONS ====================

def jarvis_camera_control(action="open"):
    """Advanced camera control like JARVIS"""
    try:
        if action == "open" or action == "activate":
            # Try to open camera app
            os.system("start microsoft.windows.camera:")
            say("Camera activated, Mr. Sohom. Visual systems online.")
            return True
        elif action == "capture" or action == "photo":
            # Capture photo using OpenCV
            cap = cv2.VideoCapture(0)
            if cap.isOpened():
                ret, frame = cap.read()
                if ret:
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"jarvis_capture_{timestamp}.jpg"
                    cv2.imwrite(filename, frame)
                    say(f"Photo captured and saved as {filename}, sir.")
                cap.release()
                return True
            else:
                say("Unable to access camera systems, sir.")
                return False
        elif action == "scan" or action == "analyze":
            say("Initiating visual analysis protocol, sir.")
            # Advanced camera analysis would go here
            return True
    except Exception as e:
        say("Camera systems are offline, sir.")
        print(f"Camera error: {e}")
        return False

def jarvis_play_specific_song(song_name, artist="", platform="youtube"):
    """Play specific songs like JARVIS with advanced search and auto-play"""
    try:
        search_query = f"{song_name} {artist}".strip()
        encoded_query = urllib.parse.quote(search_query)

        if platform.lower() == "youtube":
            # Direct YouTube Music search for better music experience
            youtube_music_url = f"https://music.youtube.com/search?q={encoded_query}"
            webbrowser.open(youtube_music_url)

            # Wait for page to load then auto-click first result
            def auto_play_youtube():
                time.sleep(4)  # Wait for page load
                try:
                    # Try multiple click positions for first song
                    click_positions = [
                        (400, 350),  # First result position
                        (350, 300),  # Alternative position
                        (450, 400),  # Another alternative
                    ]

                    for pos in click_positions:
                        try:
                            pyautogui.click(pos[0], pos[1])
                            time.sleep(1)
                            break
                        except:
                            continue

                    # Try pressing Enter as backup
                    pyautogui.press('enter')

                except Exception as e:
                    print(f"Auto-play error: {e}")

            # Run auto-play in background thread
            threading.Thread(target=auto_play_youtube, daemon=True).start()
            say(f"Now playing {song_name} by {artist} on YouTube Music, sir.")

        elif platform.lower() == "spotify":
            try:
                # Try Spotify app first
                spotify_uri = f"spotify:search:{encoded_query}"
                result = subprocess.run(['start', spotify_uri], shell=True, capture_output=True)

                if result.returncode == 0:
                    say(f"Playing {song_name} by {artist} on Spotify app, sir.")

                    # Auto-play first result after delay
                    def auto_play_spotify():
                        time.sleep(3)
                        try:
                            # Press Enter to play first result
                            pyautogui.press('enter')
                            time.sleep(1)
                            pyautogui.press('space')  # Start playback
                        except:
                            pass

                    threading.Thread(target=auto_play_spotify, daemon=True).start()
                else:
                    raise Exception("Spotify app not available")

            except:
                # Fallback to Spotify Web Player
                spotify_web_url = f"https://open.spotify.com/search/{encoded_query}"
                webbrowser.open(spotify_web_url)

                def auto_play_spotify_web():
                    time.sleep(5)  # Wait for web player to load
                    try:
                        # Click first song result
                        pyautogui.click(400, 350)
                        time.sleep(1)
                        # Click play button
                        pyautogui.click(500, 400)
                    except:
                        pass

                threading.Thread(target=auto_play_spotify_web, daemon=True).start()
                say(f"Playing {song_name} by {artist} on Spotify web player, sir.")

        return True
    except Exception as e:
        say("Music systems are experiencing difficulties, sir.")
        print(f"Music error: {e}")
        return False

def jarvis_play_youtube_video(search_term):
    """Play YouTube videos with auto-click"""
    try:
        encoded_query = urllib.parse.quote(search_term)
        youtube_url = f"https://www.youtube.com/results?search_query={encoded_query}"
        webbrowser.open(youtube_url)

        def auto_play_video():
            time.sleep(4)  # Wait for page load
            try:
                # Click on first video thumbnail
                click_positions = [
                    (320, 290),  # First video position
                    (300, 270),  # Alternative position
                    (350, 310),  # Another alternative
                ]

                for pos in click_positions:
                    try:
                        pyautogui.click(pos[0], pos[1])
                        break
                    except:
                        continue

            except Exception as e:
                print(f"Auto-play video error: {e}")

        threading.Thread(target=auto_play_video, daemon=True).start()
        say(f"Now playing {search_term} on YouTube, sir.")
        return True

    except Exception as e:
        say("Video playback systems are offline, sir.")
        print(f"YouTube video error: {e}")
        return False

def jarvis_open_spotify_and_play():
    """Open Spotify and start playing music"""
    try:
        # Try to open Spotify app
        try:
            os.system('start spotify:')
            say("Spotify activated, sir. Music systems online.")

            # Auto-play after opening
            def auto_start_music():
                time.sleep(3)
                try:
                    pyautogui.press('space')  # Play/pause toggle
                except:
                    pass

            threading.Thread(target=auto_start_music, daemon=True).start()

        except:
            # Fallback to web player
            webbrowser.open("https://open.spotify.com")
            say("Spotify web player activated, sir.")

        return True
    except Exception as e:
        say("Music systems are offline, sir.")
        return False

def jarvis_send_email_to_address(email_address, subject="", message=""):
    """Send email to specific address like JARVIS"""
    try:
        if not subject:
            subject = "Message from Sohom via SAGE AI"
        if not message:
            message = "This is an automated message sent through SAGE AI assistant."

        # Use AI to enhance the message
        enhanced_message = craft_ai_message(message, "professional")

        success = send_email(email_address, subject, enhanced_message)

        if success:
            say(f"Message transmitted to {email_address}, sir. Communication successful.")
        else:
            say("Message transmission failed, sir. Please check email configuration.")

        return success
    except Exception as e:
        say("Communication systems are offline, sir.")
        print(f"Email error: {e}")
        return False

def craft_ai_message(message, tone="professional"):
    """Use AI to enhance messages"""
    try:
        prompt = f"Enhance this message in a {tone} tone: {message}"

        url = "https://openrouter.ai/api/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {apikey}",
            "Content-Type": "application/json"
        }

        data = {
            "model": "mistralai/devstral-small:free",
            "messages": [
                {
                    "role": "system",
                    "content": f"You are a {tone} message enhancer. Improve the given message while keeping it concise."
                },
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 200
        }

        response = requests.post(url, headers=headers, json=data, timeout=15)

        if response.status_code == 200:
            result = response.json()
            return result['choices'][0]['message']['content']
        else:
            return message
    except:
        return message

def jarvis_system_status():
    """JARVIS-style system status report"""
    try:
        # Get system information
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # Network status
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            network_status = "Online"
        except:
            network_status = "Offline"

        # Battery status (if laptop)
        try:
            battery = psutil.sensors_battery()
            if battery:
                battery_status = f"{battery.percent}% {'(Charging)' if battery.power_plugged else '(On Battery)'}"
            else:
                battery_status = "Desktop System"
        except:
            battery_status = "Unknown"

        status_report = f"""
        System Status Report:
        CPU Usage: {cpu_percent}%
        Memory Usage: {memory.percent}%
        Disk Usage: {disk.percent}%
        Network: {network_status}
        Power: {battery_status}
        """

        say(f"System status: CPU at {cpu_percent}%, Memory at {memory.percent}%, Network {network_status}, sir.")
        print(status_report)

        return True
    except Exception as e:
        say("Unable to access system diagnostics, sir.")
        print(f"System status error: {e}")
        return False

def jarvis_weather_report(city=""):
    """JARVIS-style weather report"""
    try:
        if not city:
            # Try to get location automatically
            try:
                g = geocoder.ip('me')
                city = g.city if g.city else "New York"
            except:
                city = "New York"

        # Use OpenWeatherMap API (you'd need to add API key to config)
        # For now, using a placeholder response
        say(f"Weather systems indicate partly cloudy conditions in {city}, sir. Temperature approximately 22 degrees Celsius.")

        return True
    except Exception as e:
        say("Weather monitoring systems are offline, sir.")
        print(f"Weather error: {e}")
        return False

def jarvis_internet_speed():
    """Test internet speed like JARVIS"""
    try:
        say("Initiating network diagnostics, sir.")

        st = speedtest.Speedtest()
        st.get_best_server()

        download_speed = st.download() / 1_000_000  # Convert to Mbps
        upload_speed = st.upload() / 1_000_000
        ping = st.results.ping

        say(f"Network analysis complete. Download speed: {download_speed:.1f} megabits per second. Upload speed: {upload_speed:.1f} megabits per second. Latency: {ping:.1f} milliseconds, sir.")

        return True
    except Exception as e:
        say("Network diagnostics failed, sir.")
        print(f"Speed test error: {e}")
        return False

def jarvis_search_wikipedia(query):
    """JARVIS-style Wikipedia search"""
    try:
        say(f"Searching knowledge database for {query}, sir.")

        summary = wikipedia.summary(query, sentences=2)
        say(f"According to my knowledge database: {summary}")

        return True
    except Exception as e:
        say(f"Unable to find information about {query} in the knowledge database, sir.")
        return False

def jarvis_stock_price(symbol):
    """Get stock prices like JARVIS"""
    try:
        say(f"Accessing financial markets for {symbol}, sir.")

        stock = yf.Ticker(symbol)
        info = stock.info
        current_price = info.get('currentPrice', 'Unknown')

        say(f"{symbol} is currently trading at {current_price} dollars, sir.")

        return True
    except Exception as e:
        say(f"Unable to access market data for {symbol}, sir.")
        return False

def jarvis_security_protocol():
    """JARVIS-style security features"""
    try:
        say("Initiating security protocols, sir.")

        # Lock the workstation
        os.system("rundll32.exe user32.dll,LockWorkStation")

        return True
    except Exception as e:
        say("Security systems are offline, sir.")
        return False

def jarvis_power_management(action):
    """JARVIS-style power management"""
    try:
        if action == "sleep":
            say("Initiating sleep mode, sir. Systems will be on standby.")
            os.system("rundll32.exe powrprof.dll,SetSuspendState 0,1,0")
        elif action == "shutdown":
            say("Initiating shutdown sequence, sir. All systems will be offline.")
            os.system("shutdown /s /t 10")
        elif action == "restart":
            say("Initiating restart sequence, sir. Systems will be back online shortly.")
            os.system("shutdown /r /t 10")

        return True
    except Exception as e:
        say("Power management systems are offline, sir.")
        return False

def open_chrome_tab(url):
    """Open a new tab in Chrome with the specified URL"""
    try:
        # Try to open in Chrome specifically
        chrome_path = "C:/Program Files/Google/Chrome/Application/chrome.exe %s"
        webbrowser.get(chrome_path).open_new_tab(url)
        say(f"Opening {url} in Chrome")
    except:
        # Fallback to default browser
        webbrowser.open_new_tab(url)
        say(f"Opening {url} in browser")

def open_application(app_name):
    """Open Windows applications"""
    apps = {
        "notepad": "notepad",
        "calculator": "calc",
        "paint": "mspaint",
        "word": "winword",
        "excel": "excel",
        "powerpoint": "powerpnt",
        "chrome": "chrome",
        "firefox": "firefox",
        "edge": "msedge",
        "file explorer": "explorer",
        "task manager": "taskmgr",
        "control panel": "control",
        "settings": "ms-settings:",
        "command prompt": "cmd",
        "powershell": "powershell"
    }

    app_name = app_name.lower()
    if app_name in apps:
        try:
            if app_name == "settings":
                os.system(f"start {apps[app_name]}")
            else:
                subprocess.Popen(apps[app_name])
            say(f"Opening {app_name}")
            return True
        except Exception as e:
            say(f"Sorry, I couldn't open {app_name}")
            print(f"App error: {str(e)}")
            return False
    else:
        say(f"Sorry, I don't know how to open {app_name}")
        return False

def say(text, interruptible=True):
    """Enhanced text-to-speech with interruption capability like Gemini Live"""
    global is_speaking, should_stop_speaking

    try:
        # Stop any current speech if interrupted
        if is_speaking and interruptible:
            tts_engine.stop()
            should_stop_speaking = False

        is_speaking = True

        # Split text into chunks for interruptible speech
        sentences = text.split('. ')

        for i, sentence in enumerate(sentences):
            if should_stop_speaking:
                should_stop_speaking = False
                is_speaking = False
                return

            # Add period back if not last sentence
            if i < len(sentences) - 1 and not sentence.endswith('.'):
                sentence += '.'

            tts_engine.say(sentence)
            tts_engine.runAndWait()

            # Small pause between sentences for natural flow
            time.sleep(0.1)

        is_speaking = False

    except Exception as e:
        is_speaking = False
        print(f"TTS Error: {str(e)}")

def stop_speaking():
    """Stop current speech immediately"""
    global should_stop_speaking, is_speaking
    should_stop_speaking = True
    is_speaking = False
    try:
        tts_engine.stop()
    except:
        pass

def takeCommand(continuous=False, timeout=5):
    """Enhanced voice command with Gemini Live-style continuous listening"""
    global is_speaking, should_stop_speaking, conversation_active, last_interaction_time

    r = sr.Recognizer()

    # Enhanced recognizer settings for better performance
    r.energy_threshold = 300
    r.dynamic_energy_threshold = True
    r.pause_threshold = 0.8
    r.operation_timeout = None

    with sr.Microphone() as source:
        if not continuous:
            print("🎤 Adjusting for ambient noise...")
            r.adjust_for_ambient_noise(source, duration=1)

        print("🎧 Listening..." if not continuous else "🔄 Continuous listening...")

        try:
            # If SAGE is speaking, listen for interruption
            if is_speaking:
                audio = r.listen(source, timeout=1, phrase_time_limit=3)
                try:
                    interruption = r.recognize_google(audio, language="en-in")
                    # Check for interruption keywords
                    interrupt_words = ["stop", "pause", "wait", "sage", "hey", "excuse me"]
                    if any(word in interruption.lower() for word in interrupt_words):
                        stop_speaking()
                        print(f"🛑 Interrupted: {interruption}")
                        return interruption
                except:
                    pass
                return "None"

            # Normal listening
            listen_timeout = timeout if not continuous else 10
            audio = r.listen(source, timeout=listen_timeout, phrase_time_limit=15)

            print("🧠 Processing speech...")

            # Try multiple recognition approaches
            try:
                query = r.recognize_google(audio, language="en-in")
            except:
                try:
                    query = r.recognize_google(audio, language="en-us")
                except:
                    query = r.recognize_google(audio, language="en-gb")

            print(f"👤 User said: {query}")

            # Update conversation state
            conversation_active = True
            last_interaction_time = datetime.datetime.now()

            return query.lower().strip()

        except sr.WaitTimeoutError:
            if continuous:
                return "timeout_continue"
            print("⏰ Listening timeout")
            return "None"
        except sr.UnknownValueError:
            if continuous:
                return "unknown_continue"
            print("❓ Could not understand audio")
            return "None"
        except sr.RequestError as e:
            print(f"🚫 Speech service error: {e}")
            return "None"
        except Exception as e:
            print(f"❌ Error occurred: {str(e)}")
            return "Some Error Occurred. Sorry from Sage"

def continuous_conversation_mode():
    """Gemini Live-style continuous conversation"""
    global conversation_active, continuous_listening

    say("Entering continuous conversation mode. I'll keep listening until you say 'exit conversation' or stay quiet for 30 seconds.")
    continuous_listening = True
    conversation_active = True

    while continuous_listening and conversation_active:
        query = takeCommand(continuous=True, timeout=30)

        if query in ["timeout_continue", "unknown_continue"]:
            # Check if conversation should timeout
            if last_interaction_time and (datetime.datetime.now() - last_interaction_time).seconds > 30:
                say("Conversation timeout. Returning to normal mode.")
                break
            continue

        if query == "None":
            continue

        # Exit conditions
        if any(phrase in query for phrase in ["exit conversation", "stop conversation", "end conversation"]):
            say("Exiting continuous conversation mode.")
            break

        # Process the command normally
        process_voice_command(query)

    continuous_listening = False
    conversation_active = False

def process_voice_command(query):
    """Process voice commands with context awareness like Gemini Live"""
    global chatStr, conversation_active

    # Handle interruption commands first
    if any(word in query for word in ["stop", "pause", "quiet", "silence"]):
        stop_speaking()
        say("Understood, sir.")
        return

    # Context-aware responses
    if conversation_active:
        # Add conversational context
        context_phrases = [
            "Also, ", "Additionally, ", "Furthermore, ", "By the way, ",
            "Speaking of which, ", "That reminds me, "
        ]

        # Natural conversation starters
        if any(phrase in query for phrase in ["what about", "how about", "tell me about"]):
            # More conversational response
            pass

    # Process commands with the main command logic
    # This will call the main command processing logic

def enhance_response_with_context(response, query):
    """Enhance responses with conversational context like Gemini Live"""
    global conversation_active, last_interaction_time

    if not conversation_active:
        return response

    # Add conversational elements
    conversation_starters = [
        "Certainly, sir. ",
        "Of course, Mr. Sohom. ",
        "Right away, sir. ",
        "Absolutely, sir. "
    ]

    # Add follow-up suggestions
    follow_ups = {
        "music": "Would you like me to adjust the volume or skip to another track?",
        "email": "Should I send any other messages while we're at it?",
        "weather": "Would you like me to check the forecast for tomorrow as well?",
        "system": "Shall I run any system optimizations while I'm at it?",
        "search": "Would you like me to search for anything else related to this topic?"
    }

    # Detect command type and add appropriate follow-up
    for cmd_type, follow_up in follow_ups.items():
        if cmd_type in query:
            if random.random() < 0.3:  # 30% chance to add follow-up
                response += f" {follow_up}"
            break

    return response

def natural_language_processing(query):
    """Process natural language like Gemini Live"""
    # Handle conversational queries
    conversational_patterns = {
        "how are you": "I'm functioning optimally, sir. All systems are running smoothly.",
        "what can you do": "I can control music, send emails, manage your system, search for information, and much more. What would you like me to help you with?",
        "thank you": "You're very welcome, Mr. Sohom. Always happy to assist.",
        "good job": "Thank you, sir. I'm here to serve.",
        "that's wrong": "I apologize for the error, sir. Let me correct that for you.",
        "try again": "Of course, sir. Let me attempt that once more.",
        "never mind": "Understood, sir. Is there anything else I can help you with?",
        "what time": f"The current time is {datetime.datetime.now().strftime('%I:%M %p')}, sir.",
        "what day": f"Today is {datetime.datetime.now().strftime('%A, %B %d, %Y')}, sir."
    }

    for pattern, response in conversational_patterns.items():
        if pattern in query.lower():
            return response

    return None

def smart_wake_word_detection(query):
    """Detect wake words and context like Gemini Live"""
    wake_words = ["sage", "hey sage", "okay sage", "jarvis", "computer"]

    for wake_word in wake_words:
        if wake_word in query.lower():
            # Remove wake word and return clean command
            clean_query = query.lower().replace(wake_word, "").strip()
            if clean_query:
                return clean_query
            else:
                return "listening"

    return query

if __name__ == '__main__':
    print('=' * 60)
    print('🤖 SAGE A.I - ADVANCED JARVIS-STYLE ASSISTANT')
    print('=' * 60)
    print('🎤 Voice Commands Available:')
    print('📷 Camera: "activate camera", "take photo"')
    print('🎵 Music: "play [song] by [artist] on youtube/spotify"')
    print('🎬 Video: "play video [search term]", "pause music", "next song"')
    print('📧 Email: "send <NAME_EMAIL> message hello"')
    print('🔧 System: "system status", "internet speed", "weather"')
    print('🔒 Security: "security protocol", "sleep mode"')
    print('📚 Knowledge: "search wikipedia for [topic]"')
    print('💰 Finance: "stock price AAPL"')
    print('🎮 Control: "pause music", "next song", "previous song"')
    print('💬 Conversation: "continuous mode", "stop", "pause"')
    print('🛑 Interruption: Say "stop", "pause", or "sage" while I\'m speaking')
    print('=' * 60)

    say("Good day, Mr. Sohom. SAGE AI systems are online and ready for your commands. All systems operational, sir.")

    while True:
        query = takeCommand()

        # Skip processing if no valid input
        if query in ["None", "Some Error Occurred. Sorry from Sage", "timeout_continue", "unknown_continue"]:
            continue

        query = query.lower()

        # Smart wake word detection
        query = smart_wake_word_detection(query)

        if query == "listening":
            say("Yes, sir? How may I assist you?")
            continue

        # Handle interruption and conversation commands first
        if any(word in query for word in ["stop", "pause", "quiet", "silence"]) and is_speaking:
            stop_speaking()
            say("Yes, sir?")
            continue

        # Natural language processing for conversational queries
        natural_response = natural_language_processing(query)
        if natural_response:
            enhanced_response = enhance_response_with_context(natural_response, query)
            say(enhanced_response)
            continue

        # Conversation mode commands
        if "continuous mode" in query or "conversation mode" in query:
            continuous_conversation_mode()
            continue
        elif "exit conversation" in query or "stop conversation" in query:
            if continuous_listening:
                continuous_listening = False
                say("Exiting conversation mode, sir.")
            continue

        # Website opening commands
        sites = [
            ["youtube", "https://www.youtube.com"],
            ["wikipedia", "https://www.wikipedia.com"],
            ["google", "https://www.google.com"],
            ["github", "https://www.github.com"],
            ["stackoverflow", "https://www.stackoverflow.com"]
        ]

        site_opened = False
        for site in sites:
            if f"open {site[0]}" in query:
                say(f"Opening {site[0]} sir...")
                webbrowser.open(site[1])
                site_opened = True
                break

        if site_opened:
            continue
        # ==================== JARVIS-STYLE COMMANDS ====================

        # Camera Commands
        if "activate camera" in query or "open camera" in query:
            jarvis_camera_control("open")
        elif "take photo" in query or "capture image" in query:
            jarvis_camera_control("capture")
        elif "scan" in query and "visual" in query:
            jarvis_camera_control("scan")

        # Enhanced Music Commands
        elif "play" in query and ("song" in query or "music" in query or "track" in query):
            # Extract song and artist with better parsing
            if " by " in query:
                parts = query.split(" by ")
                song_part = parts[0].replace("play", "").replace("song", "").replace("music", "").replace("track", "").strip()
                artist_part = parts[1].replace("on youtube", "").replace("on spotify", "").strip()

                # Determine platform
                platform = "youtube"
                if "spotify" in query:
                    platform = "spotify"

                jarvis_play_specific_song(song_part, artist_part, platform)
            else:
                # Extract song name with better filtering
                words_to_remove = ["play", "song", "music", "track", "on", "spotify", "youtube", "the"]
                query_words = query.split()
                song_words = [word for word in query_words if word.lower() not in words_to_remove]

                if song_words:
                    song_name = " ".join(song_words)
                    platform = "youtube"
                    if "spotify" in query:
                        platform = "spotify"
                    jarvis_play_specific_song(song_name, "", platform)
                else:
                    # If no specific song, just open music service
                    if "spotify" in query:
                        jarvis_open_spotify_and_play()
                    else:
                        webbrowser.open("https://music.youtube.com")
                        say("YouTube Music activated, sir.")

        # Video playback commands
        elif "play video" in query or ("play" in query and "youtube" in query and "song" not in query):
            search_term = query.replace("play video", "").replace("play", "").replace("on youtube", "").strip()
            if search_term:
                jarvis_play_youtube_video(search_term)
            else:
                webbrowser.open("https://www.youtube.com")
                say("YouTube activated, sir.")

        # Music service commands
        elif "open spotify" in query or "start spotify" in query:
            jarvis_open_spotify_and_play()
        elif "open youtube music" in query or "start youtube music" in query:
            webbrowser.open("https://music.youtube.com")
            say("YouTube Music activated, sir.")
        elif "pause music" in query or "stop music" in query:
            try:
                pyautogui.press('space')  # Universal pause/play
                say("Music paused, sir.")
            except:
                say("Unable to control music playback, sir.")
        elif "next song" in query or "skip song" in query:
            try:
                pyautogui.hotkey('ctrl', 'right')  # Next track shortcut
                say("Skipping to next track, sir.")
            except:
                say("Unable to skip track, sir.")
        elif "previous song" in query:
            try:
                pyautogui.hotkey('ctrl', 'left')  # Previous track shortcut
                say("Playing previous track, sir.")
            except:
                say("Unable to go to previous track, sir.")

        # Email to specific address
        elif "send email to" in query and "@" in query:
            # Extract email address
            words = query.split()
            email_address = ""
            for word in words:
                if "@" in word:
                    email_address = word
                    break

            if email_address:
                # Extract message if provided
                if "message" in query:
                    message_start = query.find("message") + 7
                    message = query[message_start:].strip()
                else:
                    message = "Quick message from Sohom via SAGE AI"

                jarvis_send_email_to_address(email_address, message=message)

        # System Commands
        elif "system status" in query or "diagnostics" in query:
            jarvis_system_status()
        elif "internet speed" in query or "network speed" in query:
            jarvis_internet_speed()
        elif "weather" in query:
            city = ""
            if "in" in query:
                city_start = query.find("in") + 2
                city = query[city_start:].strip()
            jarvis_weather_report(city)

        # Knowledge Commands
        elif "search" in query and ("wikipedia" in query or "knowledge" in query):
            search_term = query.replace("search", "").replace("wikipedia", "").replace("knowledge", "").replace("for", "").strip()
            jarvis_search_wikipedia(search_term)
        elif "stock price" in query or "market" in query:
            # Extract stock symbol
            words = query.split()
            symbol = ""
            for i, word in enumerate(words):
                if word.lower() in ["stock", "price", "market"] and i + 1 < len(words):
                    symbol = words[i + 1].upper()
                    break
            if symbol:
                jarvis_stock_price(symbol)

        # Security Commands
        elif "security protocol" in query or "lock system" in query:
            jarvis_security_protocol()
        elif "sleep mode" in query:
            jarvis_power_management("sleep")
        elif "shutdown" in query and "system" in query:
            jarvis_power_management("shutdown")
        elif "restart" in query and "system" in query:
            jarvis_power_management("restart")

        # Legacy Music Commands
        elif "open music" in query or "open spotify" in query:
            play_music_on_spotify()
        elif "open youtube music" in query:
            play_music_on_youtube()

        # Time command
        elif "time" in query:
            hour = datetime.datetime.now().strftime("%H")
            minute = datetime.datetime.now().strftime("%M")
            say(f"Sir, the time is {hour} hours and {minute} minutes")

        # Enhanced Email commands
        elif "send email" in query or "send mail" in query or "write email" in query or "compose email" in query:
            try:
                # Check if contact name is mentioned in query
                contact_found = False
                recipient_name = ""

                for contact_name in CONTACTS.keys():
                    if contact_name in query:
                        recipient_name = contact_name
                        contact_found = True
                        break

                if contact_found:
                    # Check if it's a request to craft an email with AI
                    if "write" in query or "compose" in query or "craft" in query:
                        say(f"What would you like to say in the email to {recipient_name}?")
                        # In a real implementation, you'd listen for the next voice input
                        # For now, we'll use a default purpose
                        purpose = "Following up on our previous conversation"
                        send_crafted_email(recipient_name, purpose)
                    else:
                        # Simple email
                        to_email = get_contact_email(recipient_name)
                        if to_email:
                            say(f"Sending a quick message to {recipient_name}")
                            subject = "Message from Sohom via Sage AI"
                            body = "Hi,\n\nThis is a quick message sent through my AI assistant Sage.\n\nBest regards,\nSohom"
                            send_email(to_email, subject, body)
                else:
                    say("Please specify who you want to send the email to. I can send emails to contacts in my database.")

            except Exception as e:
                say("Sorry, I encountered an error while trying to send the email.")
                print(f"Email command error: {str(e)}")

        # Specific email crafting commands
        elif "craft email" in query or "write professional email" in query:
            try:
                contact_found = False
                recipient_name = ""

                for contact_name in CONTACTS.keys():
                    if contact_name in query:
                        recipient_name = contact_name
                        contact_found = True
                        break

                if contact_found:
                    # Extract purpose from query or use default
                    purpose = "Professional correspondence"
                    if "about" in query:
                        purpose_start = query.find("about") + 5
                        purpose = query[purpose_start:].strip()

                    send_crafted_email(recipient_name, purpose)
                else:
                    say("Please specify who you want to send the email to.")

            except Exception as e:
                say("Sorry, I encountered an error while crafting the email.")
                print(f"Email crafting error: {str(e)}")

        # Chrome tab opening commands
        elif "open chrome tab" in query or "new chrome tab" in query:
            if "youtube" in query:
                open_chrome_tab("https://www.youtube.com")
            elif "google" in query:
                open_chrome_tab("https://www.google.com")
            elif "gmail" in query:
                open_chrome_tab("https://mail.google.com")
            elif "github" in query:
                open_chrome_tab("https://www.github.com")
            else:
                open_chrome_tab("https://www.google.com")

        # Application opening commands
        elif "open" in query:
            # Extract app name from query
            app_name = query.replace("open", "").strip()
            if app_name:
                open_application(app_name)
            else:
                say("What application would you like me to open?")

        # AI prompt processing
        elif "using artificial intelligence" in query:
            ai(prompt=query)
            say("AI response has been generated and saved")

        # Exit command
        elif "sage quit" in query or "exit" in query or "bye" in query:
            say("Goodbye Sohom, have a great day!")
            exit()

        # Reset chat
        elif "reset chat" in query:
            chatStr = ""
            say("Chat history has been reset sir")

        # Default: Chat with AI
        else:
            print("Chatting...")
            chat(query)

