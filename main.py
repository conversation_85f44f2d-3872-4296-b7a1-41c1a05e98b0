import speech_recognition as sr
import os
import webbrowser
import requests
import json
import pyttsx3
from config import apikey
import datetime
import random
import numpy as np


chatStr = ""
# Initialize text-to-speech engine
tts_engine = pyttsx3.init()

# https://youtu.be/Z3ZAJoi4x6Q
def chat(query):
    global chatStr
    print(chatStr)
    chatStr += f"Harry: {query}\n Jarvis: "

    try:
        # OpenRouter API endpoint
        url = "https://openrouter.ai/api/v1/chat/completions"

        # Headers for OpenRouter API
        headers = {
            "Authorization": f"Bearer {apikey}",
            "Content-Type": "application/json"
        }

        # Request payload for Mistral model
        data = {
            "model": "mistralai/mistral-7b-instruct",
            "messages": [
                {
                    "role": "system",
                    "content": "You are <PERSON>, a helpful AI assistant. Keep responses concise and conversational."
                },
                {
                    "role": "user",
                    "content": chatStr + query
                }
            ],
            "temperature": 0.7,
            "max_tokens": 256,
            "top_p": 1,
            "frequency_penalty": 0,
            "presence_penalty": 0
        }

        # Make the API request
        response = requests.post(url, headers=headers, json=data)

        if response.status_code == 200:
            result = response.json()
            response_text = result['choices'][0]['message']['content']
            say(response_text)
            chatStr += f"{response_text}\n"
            return response_text
        else:
            error_msg = f"API Error: {response.status_code}"
            say(error_msg)
            return error_msg

    except Exception as e:
        error_msg = f"Error occurred: {str(e)}"
        say(error_msg)
        return error_msg


def ai(prompt):
    try:
        text = f"Mistral AI response for Prompt: {prompt} \n *************************\n\n"

        # OpenRouter API endpoint
        url = "https://openrouter.ai/api/v1/chat/completions"

        # Headers for OpenRouter API
        headers = {
            "Authorization": f"Bearer {apikey}",
            "Content-Type": "application/json"
        }

        # Request payload for Mistral model
        data = {
            "model": "mistralai/mistral-7b-instruct",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 256,
            "top_p": 1,
            "frequency_penalty": 0,
            "presence_penalty": 0
        }

        # Make the API request
        response = requests.post(url, headers=headers, json=data)

        if response.status_code == 200:
            result = response.json()
            response_text = result['choices'][0]['message']['content']
            text += response_text

            if not os.path.exists("Openai"):
                os.mkdir("Openai")

            # Create filename from prompt
            filename = ''.join(prompt.split('intelligence')[1:]).strip() if 'intelligence' in prompt else f"prompt-{random.randint(1, 2343434356)}"
            filename = filename[:50] if filename else f"prompt-{random.randint(1, 2343434356)}"  # Limit filename length

            with open(f"Openai/{filename}.txt", "w", encoding="utf-8") as f:
                f.write(text)

            print(f"Response saved to Openai/{filename}.txt")
        else:
            print(f"API Error: {response.status_code}")

    except Exception as e:
        print(f"Error in AI function: {str(e)}")

def say(text):
    """Text-to-speech function that works on Windows"""
    try:
        tts_engine.say(text)
        tts_engine.runAndWait()
    except Exception as e:
        print(f"TTS Error: {str(e)}")

def takeCommand():
    """Listen for voice commands and convert to text"""
    r = sr.Recognizer()
    with sr.Microphone() as source:
        print("Adjusting for ambient noise...")
        r.adjust_for_ambient_noise(source, duration=1)
        r.pause_threshold = 1
        print("Listening...")

        try:
            audio = r.listen(source, timeout=5, phrase_time_limit=10)
            print("Recognizing...")
            query = r.recognize_google(audio, language="en-in")
            print(f"User said: {query}")
            return query
        except sr.WaitTimeoutError:
            print("Listening timeout")
            return "None"
        except sr.UnknownValueError:
            print("Could not understand audio")
            return "None"
        except sr.RequestError as e:
            print(f"Could not request results; {e}")
            return "None"
        except Exception as e:
            print(f"Error occurred: {str(e)}")
            return "Some Error Occurred. Sorry from Jarvis"

if __name__ == '__main__':
    print('Welcome to Jarvis A.I')
    say("Welcome to Jarvis A.I")

    while True:
        query = takeCommand()

        # Skip processing if no valid input
        if query in ["None", "Some Error Occurred. Sorry from Jarvis"]:
            continue

        query = query.lower()

        # Website opening commands
        sites = [
            ["youtube", "https://www.youtube.com"],
            ["wikipedia", "https://www.wikipedia.com"],
            ["google", "https://www.google.com"],
            ["github", "https://www.github.com"],
            ["stackoverflow", "https://www.stackoverflow.com"]
        ]

        site_opened = False
        for site in sites:
            if f"open {site[0]}" in query:
                say(f"Opening {site[0]} sir...")
                webbrowser.open(site[1])
                site_opened = True
                break

        if site_opened:
            continue
        # Music command (Windows compatible)
        if "open music" in query or "play music" in query:
            try:
                # Try to open default music app on Windows
                os.system("start ms-music:")
                say("Opening music app sir...")
            except:
                say("Sorry, I couldn't open the music app")

        # Time command
        elif "time" in query:
            hour = datetime.datetime.now().strftime("%H")
            minute = datetime.datetime.now().strftime("%M")
            say(f"Sir, the time is {hour} hours and {minute} minutes")

        # Windows-specific app opening commands
        elif "open notepad" in query:
            os.system("notepad")
            say("Opening Notepad sir...")

        elif "open calculator" in query:
            os.system("calc")
            say("Opening Calculator sir...")

        elif "open browser" in query:
            webbrowser.open("https://www.google.com")
            say("Opening browser sir...")

        # AI prompt processing
        elif "using artificial intelligence" in query:
            ai(prompt=query)
            say("AI response has been generated and saved")

        # Exit command
        elif "jarvis quit" in query or "exit" in query or "bye" in query:
            say("Goodbye sir, have a great day!")
            exit()

        # Reset chat
        elif "reset chat" in query:
            chatStr = ""
            say("Chat history has been reset sir")

        # Default: Chat with AI
        else:
            print("Chatting...")
            chat(query)





        # say(query)