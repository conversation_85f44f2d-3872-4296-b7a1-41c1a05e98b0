import streamlit as st
import threading
import time
import json
from datetime import datetime
from sage_advanced import SageAI
from config import CONTACTS
import pandas as pd
import asyncio
import queue

# Page configuration
st.set_page_config(
    page_title="Sage Voice AI Assistant",
    page_icon="🎤",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Initialize Sage AI
@st.cache_resource
def get_sage_instance():
    return SageAI()

sage = get_sage_instance()

# Custom CSS for Voice AI Interface
st.markdown("""
<style>
    /* Hide Streamlit default elements */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}

    /* Main container styling */
    .main-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem;
        color: white;
    }

    /* Voice AI Header */
    .voice-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .voice-title {
        font-size: 4rem;
        font-weight: bold;
        background: linear-gradient(45deg, #fff, #e3f2fd);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 1rem;
    }

    .voice-subtitle {
        font-size: 1.5rem;
        opacity: 0.9;
        margin-bottom: 2rem;
    }

    /* Voice Control Center */
    .voice-control-center {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 30px;
        padding: 3rem;
        text-align: center;
        margin: 2rem auto;
        max-width: 600px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Microphone Animation */
    .mic-container {
        position: relative;
        margin: 2rem auto;
        width: 200px;
        height: 200px;
    }

    .mic-button {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 4rem;
        color: white;
        transition: all 0.3s ease;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    .mic-button:hover {
        transform: scale(1.05);
        box-shadow: 0 15px 40px rgba(0,0,0,0.4);
    }

    .mic-listening {
        animation: pulse 1.5s infinite;
        background: linear-gradient(45deg, #4ecdc4, #44a08d);
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    /* Status Display */
    .status-display {
        background: rgba(255, 255, 255, 0.15);
        border-radius: 20px;
        padding: 1.5rem;
        margin: 2rem 0;
        text-align: center;
    }

    .status-text {
        font-size: 1.3rem;
        font-weight: 500;
    }

    .command-text {
        font-size: 1.1rem;
        opacity: 0.8;
        margin-top: 0.5rem;
    }

    /* Response Display */
    .response-display {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 2rem;
        margin: 2rem 0;
        min-height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .response-text {
        font-size: 1.2rem;
        line-height: 1.6;
        text-align: center;
    }

    /* Quick Actions */
    .quick-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin: 2rem 0;
        flex-wrap: wrap;
    }

    .quick-action-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .quick-action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    /* Voice Visualization */
    .voice-visualizer {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 3px;
        margin: 1rem 0;
    }

    .voice-bar {
        width: 4px;
        background: linear-gradient(to top, #4ecdc4, #44a08d);
        border-radius: 2px;
        animation: voice-wave 1s infinite ease-in-out;
    }

    .voice-bar:nth-child(2) { animation-delay: 0.1s; }
    .voice-bar:nth-child(3) { animation-delay: 0.2s; }
    .voice-bar:nth-child(4) { animation-delay: 0.3s; }
    .voice-bar:nth-child(5) { animation-delay: 0.4s; }

    @keyframes voice-wave {
        0%, 100% { height: 10px; }
        50% { height: 30px; }
    }

    /* Settings Panel */
    .settings-panel {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state for voice interface
if 'is_listening' not in st.session_state:
    st.session_state.is_listening = False
if 'last_command' not in st.session_state:
    st.session_state.last_command = ""
if 'last_response' not in st.session_state:
    st.session_state.last_response = "Hello Sohom! I'm Sage, your voice AI assistant. Click the microphone to start talking to me."
if 'sage_status' not in st.session_state:
    st.session_state.sage_status = "ready"
if 'voice_enabled' not in st.session_state:
    st.session_state.voice_enabled = True

# Main container
st.markdown('<div class="main-container">', unsafe_allow_html=True)

# Voice AI Header
st.markdown("""
<div class="voice-header">
    <div class="voice-title">🎤 SAGE</div>
    <div class="voice-subtitle">Your Personal Voice AI Assistant</div>
</div>
""", unsafe_allow_html=True)

# Voice Control Center
st.markdown('<div class="voice-control-center">', unsafe_allow_html=True)

# Status Display
status_color = {
    "ready": "🟢",
    "listening": "🔵",
    "processing": "🟡",
    "speaking": "🟣",
    "error": "🔴"
}

st.markdown(f"""
<div class="status-display">
    <div class="status-text">{status_color.get(st.session_state.sage_status, "🟢")} Status: {st.session_state.sage_status.title()}</div>
    {f'<div class="command-text">Last command: "{st.session_state.last_command}"</div>' if st.session_state.last_command else ''}
</div>
""", unsafe_allow_html=True)

# Main Voice Interface
col1, col2, col3 = st.columns([1, 2, 1])

with col2:
    # Microphone Button
    mic_class = "mic-listening" if st.session_state.is_listening else ""
    mic_icon = "🎤" if not st.session_state.is_listening else "🔴"

    if st.button(f"{mic_icon}", key="main_mic", help="Click to start/stop voice input"):
        if not st.session_state.is_listening:
            st.session_state.is_listening = True
            st.session_state.sage_status = "listening"
            st.rerun()
        else:
            st.session_state.is_listening = False
            st.session_state.sage_status = "ready"
            st.rerun()

    # Voice Visualizer (when listening)
    if st.session_state.is_listening:
        st.markdown("""
        <div class="voice-visualizer">
            <div class="voice-bar"></div>
            <div class="voice-bar"></div>
            <div class="voice-bar"></div>
            <div class="voice-bar"></div>
            <div class="voice-bar"></div>
        </div>
        """, unsafe_allow_html=True)

        # Process voice input
        with st.spinner("🎤 Listening..."):
            command = sage.listen_for_command(timeout=8)
            if command not in ["timeout", "unknown", "error"]:
                st.session_state.last_command = command
                st.session_state.sage_status = "processing"

                # Process command
                response = process_command(command)
                st.session_state.last_response = response
                st.session_state.sage_status = "speaking"

                # Speak response if enabled
                if st.session_state.voice_enabled:
                    sage.say(response)

                st.session_state.sage_status = "ready"
            else:
                st.session_state.sage_status = "error"
                if command == "timeout":
                    st.session_state.last_response = "I didn't hear anything. Please try again."
                elif command == "unknown":
                    st.session_state.last_response = "I couldn't understand what you said. Please speak clearly."
                else:
                    st.session_state.last_response = "There was an error with voice recognition."

        st.session_state.is_listening = False
        st.rerun()

# Response Display
st.markdown(f"""
<div class="response-display">
    <div class="response-text">{st.session_state.last_response}</div>
</div>
""", unsafe_allow_html=True)

# Quick Actions
st.markdown("""
<div class="quick-actions">
    <div class="quick-action-btn">🎵 Music</div>
    <div class="quick-action-btn">📧 Email</div>
    <div class="quick-action-btn">🌐 Web</div>
    <div class="quick-action-btn">💻 Apps</div>
    <div class="quick-action-btn">⏰ Time</div>
</div>
""", unsafe_allow_html=True)

# Quick action buttons
col1, col2, col3, col4, col5 = st.columns(5)

with col1:
    if st.button("🎵 Spotify", key="quick_spotify"):
        sage.play_music(service="spotify")
        st.session_state.last_response = "Opening Spotify for you!"
        st.rerun()

with col2:
    if st.button("📧 Gmail", key="quick_gmail"):
        import webbrowser
        webbrowser.open("https://mail.google.com")
        st.session_state.last_response = "Opening Gmail in your browser!"
        st.rerun()

with col3:
    if st.button("🎬 YouTube", key="quick_youtube"):
        import webbrowser
        webbrowser.open("https://www.youtube.com")
        st.session_state.last_response = "Opening YouTube for you!"
        st.rerun()

with col4:
    if st.button("🧮 Calculator", key="quick_calc"):
        sage.open_application("calculator")
        st.session_state.last_response = "Opening Calculator app!"
        st.rerun()

with col5:
    if st.button("⏰ Time", key="quick_time"):
        current_time = datetime.now().strftime("%I:%M %p")
        st.session_state.last_response = f"The current time is {current_time}"
        if st.session_state.voice_enabled:
            sage.say(st.session_state.last_response)
        st.rerun()

st.markdown('</div>', unsafe_allow_html=True)  # Close voice-control-center

# Settings Panel
with st.expander("⚙️ Voice Settings", expanded=False):
    st.session_state.voice_enabled = st.checkbox("🔊 Enable Text-to-Speech", value=st.session_state.voice_enabled)
    voice_timeout = st.slider("🎤 Voice Timeout (seconds)", 3, 15, 8)

    col1, col2 = st.columns(2)
    with col1:
        if st.button("🔄 Reset Sage"):
            st.session_state.last_command = ""
            st.session_state.last_response = "Hello Sohom! I'm ready to help you again."
            st.session_state.sage_status = "ready"
            sage.chat_history = []
            st.rerun()

    with col2:
        if st.button("🧪 Test Voice"):
            sage.say("Hello Sohom! Voice test successful.")
            st.session_state.last_response = "Voice test completed!"
            st.rerun()

# Voice Commands Help
with st.expander("🎤 Voice Commands", expanded=False):
    st.markdown("""
    **🎵 Music Commands:**
    - "Play [song name] on Spotify"
    - "Open YouTube Music"
    - "Play romantic songs"

    **📧 Email Commands:**
    - "Send email to [contact]"
    - "Craft email about [topic]"
    - "Write professional email"

    **🌐 Web & Apps:**
    - "Open YouTube"
    - "Open Calculator"
    - "New Chrome tab"

    **💬 General:**
    - "What time is it?"
    - "How are you?"
    - "Tell me a joke"
    """)

# Contacts Display
if CONTACTS:
    with st.expander("📞 My Contacts", expanded=False):
        for name, email in CONTACTS.items():
            st.text(f"👤 {name.title()}: {email}")

st.markdown('</div>', unsafe_allow_html=True)  # Close main-container

# Functions
def process_command(command: str) -> str:
    """Process user command and return response"""
    try:
        command = command.lower().strip()
        
        # Music commands
        if "play" in command and ("song" in command or "music" in command):
            service = "spotify"
            if "youtube" in command:
                service = "youtube"
            elif "apple" in command:
                service = "apple"
            
            # Extract song name
            words_to_remove = ["play", "song", "music", "on", "spotify", "youtube", "apple"]
            song_words = [word for word in command.split() if word not in words_to_remove]
            song_name = " ".join(song_words) if song_words else ""
            
            sage.play_music(song_name, service)
            return f"Playing {song_name if song_name else 'music'} on {service}"
        
        # Email commands
        elif "send email" in command or "email" in command:
            for contact_name in CONTACTS.keys():
                if contact_name in command:
                    subject, body = sage.craft_email_with_ai(contact_name, "Quick message from Sage AI")
                    success = sage.send_email(CONTACTS[contact_name], subject, body)
                    return f"Email {'sent' if success else 'failed to send'} to {contact_name}"
            return "Please specify a contact name for the email"
        
        # App opening commands
        elif "open" in command:
            app_name = command.replace("open", "").strip()
            if app_name in ["youtube", "google", "gmail"]:
                import webbrowser
                urls = {
                    "youtube": "https://www.youtube.com",
                    "google": "https://www.google.com", 
                    "gmail": "https://mail.google.com"
                }
                webbrowser.open(urls[app_name])
                return f"Opening {app_name}"
            else:
                success = sage.open_application(app_name)
                return f"{'Opened' if success else 'Failed to open'} {app_name}"
        
        # Time command
        elif "time" in command:
            current_time = datetime.now().strftime("%H:%M:%S")
            return f"The current time is {current_time}"
        
        # Default: Chat with AI
        else:
            response = sage.chat_with_ai(command)
            if tts_enabled:
                sage.say(response)
            return response
            
    except Exception as e:
        return f"Sorry, I encountered an error: {str(e)}"

# Footer
st.markdown("---")
st.markdown("""
<div style='text-align: center; color: #666;'>
    <p>Sage AI Assistant v2.0 | Built with Streamlit | Powered by Mistral AI</p>
    <p>👨‍💻 Created for Sohom | 🚀 Advanced Voice & Web Interface</p>
</div>
""", unsafe_allow_html=True)

# Auto-refresh for real-time updates
if st.session_state.is_listening:
    time.sleep(0.1)
    st.rerun()
