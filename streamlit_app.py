import streamlit as st
import threading
import time
import json
from datetime import datetime
from sage_advanced import SageA<PERSON>
from config import CONTACTS
import pandas as pd

# Page configuration
st.set_page_config(
    page_title="Sage AI Assistant",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize Sage AI
@st.cache_resource
def get_sage_instance():
    return SageAI()

sage = get_sage_instance()

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .chat-message {
        padding: 1rem;
        border-radius: 10px;
        margin: 0.5rem 0;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .sage-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
    .feature-card {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #dee2e6;
        margin: 1rem 0;
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    .status-online {
        background-color: #4caf50;
    }
    .status-offline {
        background-color: #f44336;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'is_listening' not in st.session_state:
    st.session_state.is_listening = False
if 'sage_status' not in st.session_state:
    st.session_state.sage_status = "online"

# Header
st.markdown('<h1 class="main-header">🤖 Sage AI Assistant</h1>', unsafe_allow_html=True)
st.markdown(f'<div><span class="status-indicator status-{st.session_state.sage_status}"></span>Status: {st.session_state.sage_status.title()}</div>', unsafe_allow_html=True)

# Sidebar
with st.sidebar:
    st.header("🎛️ Control Panel")
    
    # Voice Control Section
    st.subheader("🎤 Voice Control")
    
    col1, col2 = st.columns(2)
    with col1:
        if st.button("🎤 Start Listening", type="primary"):
            st.session_state.is_listening = True
            with st.spinner("Listening..."):
                command = sage.listen_for_command(timeout=10)
                if command not in ["timeout", "unknown", "error"]:
                    st.session_state.chat_history.append({
                        "user": command,
                        "timestamp": datetime.now().strftime("%H:%M:%S")
                    })
                    
                    # Process command
                    response = process_command(command)
                    st.session_state.chat_history[-1]["sage"] = response
                    st.rerun()
                else:
                    st.warning(f"Voice recognition result: {command}")
            st.session_state.is_listening = False
    
    with col2:
        if st.button("🔇 Stop"):
            st.session_state.is_listening = False
    
    # Quick Actions
    st.subheader("⚡ Quick Actions")
    
    if st.button("🎵 Open Spotify"):
        sage.play_music(service="spotify")
        st.success("Opening Spotify...")
    
    if st.button("🎬 Open YouTube"):
        import webbrowser
        webbrowser.open("https://www.youtube.com")
        st.success("Opening YouTube...")
    
    if st.button("📧 Gmail"):
        import webbrowser
        webbrowser.open("https://mail.google.com")
        st.success("Opening Gmail...")
    
    # Contacts
    st.subheader("📞 Contacts")
    if CONTACTS:
        for name, email in CONTACTS.items():
            st.text(f"{name.title()}: {email}")
    else:
        st.info("No contacts configured")
    
    # Settings
    st.subheader("⚙️ Settings")
    tts_enabled = st.checkbox("Enable Text-to-Speech", value=True)
    voice_timeout = st.slider("Voice Timeout (seconds)", 3, 15, 5)

# Main content area
col1, col2 = st.columns([2, 1])

with col1:
    st.header("💬 Chat Interface")
    
    # Text input for commands
    user_input = st.text_input("Type your command here:", placeholder="Ask Sage anything...")
    
    col_send, col_clear = st.columns([1, 1])
    with col_send:
        if st.button("Send", type="primary") and user_input:
            st.session_state.chat_history.append({
                "user": user_input,
                "timestamp": datetime.now().strftime("%H:%M:%S")
            })
            
            response = process_command(user_input)
            st.session_state.chat_history[-1]["sage"] = response
            st.rerun()
    
    with col_clear:
        if st.button("Clear Chat"):
            st.session_state.chat_history = []
            sage.chat_history = []
            st.rerun()
    
    # Chat display
    chat_container = st.container()
    with chat_container:
        for i, chat in enumerate(reversed(st.session_state.chat_history[-10:])):  # Show last 10 messages
            st.markdown(f"""
            <div class="chat-message user-message">
                <strong>You ({chat['timestamp']}):</strong> {chat['user']}
            </div>
            """, unsafe_allow_html=True)
            
            if 'sage' in chat:
                st.markdown(f"""
                <div class="chat-message sage-message">
                    <strong>Sage:</strong> {chat['sage']}
                </div>
                """, unsafe_allow_html=True)

with col2:
    st.header("🚀 Features")
    
    # Feature cards
    features = [
        {
            "title": "🎵 Music Control",
            "description": "Play songs on Spotify, YouTube Music, Apple Music",
            "commands": ["play [song] on spotify", "open youtube music"]
        },
        {
            "title": "📧 Email Management",
            "description": "Send and craft emails with AI assistance",
            "commands": ["send email to [contact]", "craft email about [topic]"]
        },
        {
            "title": "🌐 Web Navigation",
            "description": "Open websites and Chrome tabs",
            "commands": ["open youtube", "new chrome tab"]
        },
        {
            "title": "💻 App Control",
            "description": "Launch Windows applications",
            "commands": ["open calculator", "open notepad"]
        },
        {
            "title": "🤖 AI Chat",
            "description": "Intelligent conversation with context memory",
            "commands": ["ask anything", "remember our conversation"]
        }
    ]
    
    for feature in features:
        with st.expander(feature["title"]):
            st.write(feature["description"])
            st.write("**Example commands:**")
            for cmd in feature["commands"]:
                st.code(cmd)

# Functions
def process_command(command: str) -> str:
    """Process user command and return response"""
    try:
        command = command.lower().strip()
        
        # Music commands
        if "play" in command and ("song" in command or "music" in command):
            service = "spotify"
            if "youtube" in command:
                service = "youtube"
            elif "apple" in command:
                service = "apple"
            
            # Extract song name
            words_to_remove = ["play", "song", "music", "on", "spotify", "youtube", "apple"]
            song_words = [word for word in command.split() if word not in words_to_remove]
            song_name = " ".join(song_words) if song_words else ""
            
            sage.play_music(song_name, service)
            return f"Playing {song_name if song_name else 'music'} on {service}"
        
        # Email commands
        elif "send email" in command or "email" in command:
            for contact_name in CONTACTS.keys():
                if contact_name in command:
                    subject, body = sage.craft_email_with_ai(contact_name, "Quick message from Sage AI")
                    success = sage.send_email(CONTACTS[contact_name], subject, body)
                    return f"Email {'sent' if success else 'failed to send'} to {contact_name}"
            return "Please specify a contact name for the email"
        
        # App opening commands
        elif "open" in command:
            app_name = command.replace("open", "").strip()
            if app_name in ["youtube", "google", "gmail"]:
                import webbrowser
                urls = {
                    "youtube": "https://www.youtube.com",
                    "google": "https://www.google.com", 
                    "gmail": "https://mail.google.com"
                }
                webbrowser.open(urls[app_name])
                return f"Opening {app_name}"
            else:
                success = sage.open_application(app_name)
                return f"{'Opened' if success else 'Failed to open'} {app_name}"
        
        # Time command
        elif "time" in command:
            current_time = datetime.now().strftime("%H:%M:%S")
            return f"The current time is {current_time}"
        
        # Default: Chat with AI
        else:
            response = sage.chat_with_ai(command)
            if tts_enabled:
                sage.say(response)
            return response
            
    except Exception as e:
        return f"Sorry, I encountered an error: {str(e)}"

# Footer
st.markdown("---")
st.markdown("""
<div style='text-align: center; color: #666;'>
    <p>Sage AI Assistant v2.0 | Built with Streamlit | Powered by Mistral AI</p>
    <p>👨‍💻 Created for Sohom | 🚀 Advanced Voice & Web Interface</p>
</div>
""", unsafe_allow_html=True)

# Auto-refresh for real-time updates
if st.session_state.is_listening:
    time.sleep(0.1)
    st.rerun()
