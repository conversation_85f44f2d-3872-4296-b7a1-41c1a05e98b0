# 🤖 Sage AI Assistant

An advanced AI-powered voice assistant with web interface, built for Sohom using Streamlit and Mistral AI.

## ✨ Features

### 🎤 Voice Control
- Real-time speech recognition
- Natural language processing
- Voice commands for all features

### 🎵 Music Integration
- **Spotify**: Play songs directly in the app
- **YouTube Music**: Search and play music
- **Apple Music**: Access your music library
- Voice commands: "Play [song] on Spotify"

### 📧 Smart Email Management
- AI-powered email composition
- Contact management
- Professional email templates
- Voice commands: "Send email to [contact]"

### 🌐 Web & App Control
- Open websites and Chrome tabs
- Launch Windows applications
- Browser automation
- Voice commands: "Open YouTube", "Open Calculator"

### 🤖 Advanced AI Chat
- Conversation memory and context
- Powered by Mistral 7B model
- Intelligent responses
- Personalized for Sohom

## 🚀 Quick Start

### Option 1: Automated Deployment
```bash
python deploy.py
```

### Option 2: Manual Setup
1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Settings**
   - Update `config.py` with your API keys and email settings
   - Add your contacts to the CONTACTS dictionary

3. **Run the App**
   ```bash
   streamlit run streamlit_app.py
   ```

4. **Access the Interface**
   - Open your browser to `http://localhost:8501`
   - Start using voice commands or text input

## ⚙️ Configuration

### API Keys Required
- **OpenRouter API Key**: Already configured for Mistral AI
- **Email Credentials**: Update in `config.py` for email features

### Email Setup
1. Enable 2-factor authentication on Gmail
2. Generate an app password
3. Update `EMAIL_CONFIG` in `config.py`

### Contacts
Add your contacts in `config.py`:
```python
CONTACTS = {
    "mom": "<EMAIL>",
    "friend": "<EMAIL>",
    # Add more contacts...
}
```

## 🎯 Voice Commands

### Music Control
- "Play [song name] on Spotify"
- "Play music on YouTube"
- "Open Apple Music"

### Email Management
- "Send email to [contact name]"
- "Craft email about [topic]"
- "Write professional email to [contact]"

### Web & Apps
- "Open YouTube"
- "Open Calculator" 
- "New Chrome tab"
- "Open Gmail"

### General Chat
- "What's the time?"
- "How are you?"
- "Tell me a joke"
- Any general conversation

## 🏗️ Architecture

```
Sage AI Assistant
├── sage_advanced.py      # Core AI logic and functions
├── streamlit_app.py       # Web interface and UI
├── main.py               # Original command-line version
├── config.py             # Configuration and settings
├── deploy.py             # Deployment automation
└── requirements.txt      # Python dependencies
```

## 🔧 Advanced Features

### Real-time Voice Processing
- Ambient noise adjustment
- Timeout handling
- Multiple language support

### AI-Powered Email Crafting
- Context-aware email generation
- Professional tone adjustment
- Template customization

### Multi-Service Music Integration
- Direct app launching
- Web fallback options
- Search query optimization

### Conversation Memory
- Context retention across sessions
- Personalized responses
- Chat history management

## 🌐 Deployment Options

### Local Development
- Run on localhost:8501
- Full feature access
- Voice recognition enabled

### Cloud Deployment (Future)
- Streamlit Cloud integration
- Environment variable configuration
- Scalable architecture

## 🛠️ Troubleshooting

### Voice Recognition Issues
- Check microphone permissions
- Adjust ambient noise settings
- Verify audio drivers

### Email Problems
- Verify Gmail app password
- Check SMTP settings
- Ensure 2FA is enabled

### Music Integration
- Install Spotify/music apps
- Check browser permissions
- Verify internet connection

## 📱 Mobile Support
- Responsive web interface
- Touch-friendly controls
- Mobile voice recognition

## 🔒 Security
- Local API key storage
- Encrypted email credentials
- No data transmission to external servers

## 🤝 Contributing
This is a personal AI assistant for Sohom. For feature requests or issues, please contact the developer.

## 📄 License
Private project - All rights reserved.

---

**Built with ❤️ for Sohom | Powered by Mistral AI & Streamlit**
